"""
测试单个URL文件读取功能
"""
import os

def read_urls_from_file():
    """从测试.txt文件读取URL列表，返回URL字符串"""
    file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "测试.txt")
    
    if not os.path.exists(file_path):
        print(f"❌ 未找到文件: {file_path}")
        return ""
    
    urls = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue
                
                # 解析格式：手机号----memberId----URL
                parts = line.split('----')
                if len(parts) >= 3:
                    phone = parts[0]
                    member_id = parts[1]
                    url = parts[2]
                    urls.append(url)
                    print(f"📱 读取账号 {line_num}: {phone[:3]}****{phone[-4:]} -> URL已获取")
                else:
                    print(f"⚠️ 第{line_num}行格式错误，跳过: {line}")
        
        print(f"✅ 成功从文件读取到 {len(urls)} 个账号")
        # 用特殊分隔符连接所有URL，避免与URL中的&冲突
        return "||||".join(urls)
        
    except Exception as e:
        print(f"❌ 读取文件失败: {str(e)}")
        return ""

def test_url_parsing():
    """测试URL解析逻辑"""
    print("=" * 60)
    print("测试单个URL文件读取")
    print("=" * 60)
    
    # 读取文件
    token = read_urls_from_file()
    
    if not token:
        print("❌ 未能从文件读取到有效的URL")
        return
    
    print(f"\n读取到的token长度: {len(token)} 字符")
    print(f"Token内容预览: {token[:100]}...")
    
    # 测试分割逻辑
    print(f"\n测试分割逻辑:")
    print(f"是否包含||||: {'是' if '||||' in token else '否'}")
    print(f"是否为顺丰URL: {'是' if token.startswith('https://') and 'sf-express.com' in token else '否'}")
    
    # 根据修改后的逻辑进行分割
    if "||||" in token:
        print("使用||||分隔符分割")
        tokens = token.split('||||')
    elif token.startswith('https://') and 'sf-express.com' in token:
        print("识别为单个顺丰URL，直接使用")
        tokens = [token]
    else:
        print("使用&分隔符分割")
        tokens = token.split('&')
    
    tokens = [t.strip() for t in tokens if t.strip()]
    
    print(f"\n分割结果:")
    print(f"Token数量: {len(tokens)}")
    
    for i, t in enumerate(tokens, 1):
        print(f"Token {i}: 长度={len(t)}, 预览={t[:80]}...")
        
        # 验证是否为有效的顺丰URL
        if t.startswith('https://') and 'sf-express.com' in t:
            print(f"  ✅ 有效的顺丰URL")
        else:
            print(f"  ❌ 无效的URL")
    
    return tokens

def test_comparison():
    """对比修改前后的效果"""
    print(f"\n" + "=" * 60)
    print("修改前后对比")
    print("=" * 60)
    
    # 模拟原始token
    token = read_urls_from_file()
    
    if not token:
        return
    
    print("修改前的逻辑:")
    print("1. 检查是否包含||||")
    print("2. 如果不包含，直接用&分割")
    print("3. 结果：URL被错误分割成多个片段")
    
    # 模拟修改前的错误分割
    old_tokens = token.split('&')
    old_tokens = [t.strip() for t in old_tokens if t.strip()]
    print(f"修改前分割结果: {len(old_tokens)} 个片段")
    
    print(f"\n修改后的逻辑:")
    print("1. 检查是否包含||||")
    print("2. 如果不包含，检查是否为单个顺丰URL")
    print("3. 如果是，直接作为一个token使用")
    print("4. 结果：正确识别为单个完整URL")
    
    # 使用修改后的逻辑
    if "||||" in token:
        new_tokens = token.split('||||')
    elif token.startswith('https://') and 'sf-express.com' in token:
        new_tokens = [token]
    else:
        new_tokens = token.split('&')
    new_tokens = [t.strip() for t in new_tokens if t.strip()]
    print(f"修改后分割结果: {len(new_tokens)} 个完整URL")

if __name__ == "__main__":
    try:
        # 测试URL解析
        tokens = test_url_parsing()
        
        # 对比修改前后
        test_comparison()
        
        print(f"\n" + "=" * 60)
        print("测试完成！")
        print("=" * 60)
        print("修改说明:")
        print("1. ✅ 添加了单个顺丰URL的识别逻辑")
        print("2. ✅ 避免了URL中&符号导致的错误分割")
        print("3. ✅ 保持了多URL文件的兼容性")
        print("4. ✅ 保持了环境变量的兼容性")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
