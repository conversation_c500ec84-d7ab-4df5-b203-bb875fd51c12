"""
对比老版本和新版本处理URL的差异
"""
import hashlib
import json
import os
import random
import time
import sys
from datetime import datetime, timedelta
import requests
from requests.packages.urllib3.exceptions import InsecureRequestWarning
from urllib.parse import unquote

# 禁用安全请求警告
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

def Log(cont=''):
    print(cont)

class OldVersionRUN:
    """老版本的RUN类"""
    def __init__(self, url, index):
        self.index = index + 1
        self.s = requests.session()
        self.s.verify = False
        
        self.headers = {
            'Host': 'mcs-mimp-web.sf-express.com',
            'upgrade-insecure-requests': '1',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090551) XWEB/6945 Flue',
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'sec-fetch-site': 'none',
            'sec-fetch-mode': 'navigate',
            'sec-fetch-user': '?1',
            'sec-fetch-dest': 'document',
            'accept-language': 'zh-CN,zh',
            'platform': 'MINI_PROGRAM',
        }
        
        self.login_res = self.login(url)
        
    def get_deviceId(self, characters='abcdef0123456789'):
        result = ''
        for char in 'xxxxxxxx-xxxx-xxxx':
            if char == 'x':
                result += random.choice(characters)
            elif char == 'X':
                result += random.choice(characters).upper()
            else:
                result += char
        return result

    def login(self, sfurl):
        try:
            # 老版本方式：直接使用原始URL
            print(f'👉 老版本账号{self.index}正在登录...')
            ress = self.s.get(sfurl, headers=self.headers)
            
            # 获取用户信息
            self.user_id = self.s.cookies.get_dict().get('_login_user_id_', '')
            self.phone = self.s.cookies.get_dict().get('_login_mobile_', '')
            self.mobile = self.phone[:3] + "*" * 4 + self.phone[7:] if self.phone else ''
            
            cookies = self.s.cookies.get_dict()
            print(f'老版本响应状态: {ress.status_code}')
            print(f'老版本cookies数量: {len(cookies)}')
            print(f'老版本cookies: {list(cookies.keys())}')
            
            if self.phone:
                Log(f'👤 老版本账号{self.index}:【{self.mobile}】登陆成功')
                return True
            else:
                Log(f'❌ 老版本账号{self.index}获取用户信息失败')
                return False
        except Exception as e:
            Log(f'❌ 老版本登录异常: {str(e)}')
            return False

    def getSign(self):
        timestamp = str(int(round(time.time() * 1000)))
        token = 'wwesldfs29aniversaryvdld29'
        sysCode = 'MCS-MIMP-CORE'
        data = f'token={token}&timestamp={timestamp}&sysCode={sysCode}'
        signature = hashlib.md5(data.encode()).hexdigest()
        data = {
            'sysCode': sysCode,
            'timestamp': timestamp,
            'signature': signature
        }
        self.headers.update(data)
        return data

    def do_request(self, url, data={}, req_type='post', max_retries=3):
        self.getSign()
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                if req_type.lower() == 'get':
                    response = self.s.get(url, headers=self.headers, timeout=30)
                elif req_type.lower() == 'post':
                    response = self.s.post(url, headers=self.headers, json=data, timeout=30)
                else:
                    raise ValueError('Invalid req_type: %s' % req_type)
                    
                response.raise_for_status()
                
                try:
                    res = response.json()
                    return res
                except json.JSONDecodeError as e:
                    print(f'JSON解析失败: {str(e)}, 响应内容: {response.text[:200]}')
                    retry_count += 1
                    if retry_count < max_retries:
                        print(f'正在进行第{retry_count + 1}次重试...')
                        time.sleep(2)
                        continue
                    return None
                    
            except requests.exceptions.RequestException as e:
                retry_count += 1
                if retry_count < max_retries:
                    print(f'请求失败，正在重试 ({retry_count}/{max_retries}): {str(e)}')
                    time.sleep(2)
                else:
                    print('请求最终失败:', e)
                    return None
                
        return None

    def get_honeyTaskListStart(self):
        print('🍯 老版本开始获取采蜜换大礼任务列表')
        json_data = {}
        self.headers['channel'] = 'wxwdsj'
        url = 'https://mcs-mimp-web.sf-express.com/mcs-mimp/commonPost/~memberNonactivity~receiveExchangeIndexService~taskDetail'

        response = self.do_request(url, data=json_data)
        print(f'老版本采蜜任务响应: {response}')
        if response and response.get('success') == True:
            print(f'✅ 老版本采蜜任务获取成功')
            if 'obj' in response and 'list' in response['obj']:
                print(f'老版本任务数量: {len(response["obj"]["list"])}')
                for item in response["obj"]["list"]:
                    print(f'老版本任务: {item.get("taskType", "未知")} - 状态: {item.get("status", "未知")}')
            return True
        else:
            error_msg = response.get('errorMessage', '无返回') if response else '无响应'
            print(f'❌ 老版本采蜜任务获取失败: {error_msg}')
            return False

class NewVersionRUN:
    """新版本的RUN类"""
    def __init__(self, url, index):
        self.index = index + 1
        self.s = requests.session()
        self.s.verify = False
        
        self.headers = {
            'Host': 'mcs-mimp-web.sf-express.com',
            'upgrade-insecure-requests': '1',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090551) XWEB/6945 Flue',
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'sec-fetch-site': 'none',
            'sec-fetch-mode': 'navigate',
            'sec-fetch-user': '?1',
            'sec-fetch-dest': 'document',
            'accept-language': 'zh-CN,zh',
            'platform': 'MINI_PROGRAM',
        }
        
        self.login_res = self.login(url)

    def login(self, sfurl):
        try:
            # 新版本方式：直接使用原始URL（已修复）
            Log(f'🔗 新版本账号{self.index}开始登录')
            
            ress = self.s.get(sfurl, headers=self.headers, timeout=30)
            
            Log(f'📊 新版本账号{self.index}响应状态: {ress.status_code}')
            
            cookies = self.s.cookies.get_dict()
            Log(f'🍪 新版本账号{self.index}收到cookies数量: {len(cookies)}')
            
            self.user_id = cookies.get('_login_user_id_', '')
            self.phone = cookies.get('_login_mobile_', '')
            
            if len(cookies) > 0:
                cookie_list = [f"{k}={v}" for k, v in cookies.items()]
                Log(f'🍪 新版本账号{self.index}cookies: {"; ".join(cookie_list)}')
            
            self.mobile = self.phone[:3] + "*" * 4 + self.phone[7:] if self.phone else ''
            
            if self.phone:
                Log(f'👤 新版本账号{self.index}:【{self.mobile}】登陆成功')
                Log(f'🆔 新版本账号{self.index}用户ID: {self.user_id}')
                return True
            else:
                Log(f'❌ 新版本账号{self.index}获取用户信息失败')
                Log(f'🔍 新版本账号{self.index}状态码: {ress.status_code}, cookies数量: {len(cookies)}')
                
                if cookies:
                    Log(f'🔍 新版本账号{self.index}实际cookies: {list(cookies.keys())}')
                
                return False
        except Exception as e:
            Log(f'❌ 新版本账号{self.index}登录异常: {str(e)}')
            return False

    def getSign(self):
        timestamp = str(int(round(time.time() * 1000)))
        token = 'wwesldfs29aniversaryvdld29'
        sysCode = 'MCS-MIMP-CORE'
        data = f'token={token}&timestamp={timestamp}&sysCode={sysCode}'
        signature = hashlib.md5(data.encode()).hexdigest()
        data = {
            'sysCode': sysCode,
            'timestamp': timestamp,
            'signature': signature
        }
        self.headers.update(data)
        return data

    def do_request(self, url, data={}, req_type='post', max_retries=3):
        self.getSign()
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                if req_type.lower() == 'get':
                    response = self.s.get(url, headers=self.headers, timeout=30)
                elif req_type.lower() == 'post':
                    response = self.s.post(url, headers=self.headers, json=data, timeout=30)
                else:
                    raise ValueError('Invalid req_type: %s' % req_type)
                    
                response.raise_for_status()
                
                try:
                    res = response.json()
                    return res
                except json.JSONDecodeError as e:
                    print(f'JSON解析失败: {str(e)}, 响应内容: {response.text[:200]}')
                    retry_count += 1
                    if retry_count < max_retries:
                        print(f'正在进行第{retry_count + 1}次重试...')
                        time.sleep(2)
                        continue
                    return None
                    
            except requests.exceptions.RequestException as e:
                retry_count += 1
                if retry_count < max_retries:
                    print(f'请求失败，正在重试 ({retry_count}/{max_retries}): {str(e)}')
                    time.sleep(2)
                else:
                    print('请求最终失败:', e)
                    return None
                
        return None

    def get_honeyTaskListStart(self):
        print('🍯 新版本开始获取采蜜换大礼任务列表')
        json_data = {}
        self.headers['channel'] = 'wxwdsj'
        url = 'https://mcs-mimp-web.sf-express.com/mcs-mimp/commonPost/~memberNonactivity~receiveExchangeIndexService~taskDetail'
        
        response = self.do_request(url, data=json_data)
        print(f'新版本采蜜任务响应: {response}')
        if response and response.get('success') == True:
            print(f'✅ 新版本采蜜任务获取成功')
            if 'obj' in response and 'list' in response['obj']:
                print(f'新版本任务数量: {len(response["obj"]["list"])}')
                for item in response["obj"]["list"]:
                    print(f'新版本任务: {item.get("taskType", "未知")} - 状态: {item.get("status", "未知")}')
            return True
        else:
            error_msg = response.get('errorMessage', '无返回') if response else '无响应'
            print(f'❌ 新版本采蜜任务获取失败: {error_msg}')
            return False

def test_url_comparison():
    """对比测试URL"""
    test_url = "https://mcs-mimp-web.sf-express.com/mcs-mimp/share/weChat/shareGiftReceiveRedirect?source=CX&scene=6&openId=6tB15T6k0ZvyroDMcHJm6XG4WdZ0V1SUTxA9ZutcCj0DdjSr%2F6X0WoiZtgGzs7sG&memId=d97Q3fSsQut3wngIwPDenDojXNFwFq75bZ6HcYnGPBwDdjSr%2F6X0WoiZtgGzs7sG&memNo=6tB15T6k0ZvyroDMcHJm6XG4WdZ0V1SUTxA9ZutcCj0DdjSr%2F6X0WoiZtgGzs7sG&mobile=rLfLkJ2OpnPOH0mB82gAew%3D%3D&bizCode=619%40%40Mmh0VWdPUGxrMExNZmlBWDF1SVJHSDhOcHBjNlpFL3pIQ0cyTWpzWEhqRT0%3D&mediaCode=miniBd&cx-at-sign=C71B1E93F9DAB3B12A199F69FD2B2585CF1D33519A1CA5240DFAAE0CF8799DCA&cx-at-ts=1751464944&cx-at-nonce=NDV4jmNgQHG775G2nj7Cj"
    
    print("=" * 80)
    print("对比测试老版本和新版本处理URL的差异")
    print("=" * 80)
    print(f"测试URL: {test_url[:80]}...")
    
    # 测试老版本
    print(f"\n" + "=" * 40)
    print("测试老版本")
    print("=" * 40)
    
    old_run = OldVersionRUN(test_url, 0)
    old_login_success = old_run.login_res
    
    if old_login_success:
        print(f"✅ 老版本登录成功，开始测试采蜜任务")
        old_honey_success = old_run.get_honeyTaskListStart()
    else:
        print(f"❌ 老版本登录失败，跳过采蜜任务测试")
        old_honey_success = False
    
    # 测试新版本
    print(f"\n" + "=" * 40)
    print("测试新版本")
    print("=" * 40)
    
    new_run = NewVersionRUN(test_url, 0)
    new_login_success = new_run.login_res
    
    if new_login_success:
        print(f"✅ 新版本登录成功，开始测试采蜜任务")
        new_honey_success = new_run.get_honeyTaskListStart()
    else:
        print(f"❌ 新版本登录失败，跳过采蜜任务测试")
        new_honey_success = False
    
    # 总结对比结果
    print(f"\n" + "=" * 80)
    print("对比结果总结")
    print("=" * 80)
    print(f"老版本登录: {'✅ 成功' if old_login_success else '❌ 失败'}")
    print(f"新版本登录: {'✅ 成功' if new_login_success else '❌ 失败'}")
    print(f"老版本采蜜任务: {'✅ 成功' if old_honey_success else '❌ 失败'}")
    print(f"新版本采蜜任务: {'✅ 成功' if new_honey_success else '❌ 失败'}")
    
    if old_login_success and not new_login_success:
        print(f"\n🔍 分析: 新版本登录失败，需要检查登录逻辑")
    elif old_login_success and new_login_success:
        if old_honey_success and not new_honey_success:
            print(f"\n🔍 分析: 登录都成功，但新版本采蜜任务失败，需要检查请求头或签名")
        elif old_honey_success and new_honey_success:
            print(f"\n🎉 分析: 两个版本都正常工作")
        else:
            print(f"\n🔍 分析: 两个版本采蜜任务都失败，可能是服务器问题")
    else:
        print(f"\n🔍 分析: 两个版本登录都失败，可能是URL过期或服务器问题")

if __name__ == "__main__":
    try:
        test_url_comparison()
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
