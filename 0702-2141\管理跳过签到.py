import os

def load_skip_list():
    """加载跳过签到列表"""
    skip_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "跳过签到.txt")
    skip_list = []
    if os.path.exists(skip_file):
        try:
            with open(skip_file, 'r', encoding='utf-8') as f:
                for line in f:
                    phone = line.strip()
                    if phone:
                        skip_list.append(phone)
        except Exception as e:
            print(f"读取跳过签到列表失败: {str(e)}")
    return skip_list

def save_skip_list(skip_list):
    """保存跳过签到列表"""
    skip_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "跳过签到.txt")
    try:
        with open(skip_file, 'w', encoding='utf-8') as f:
            for phone in skip_list:
                f.write(f"{phone}\n")
        print(f"✅ 跳过签到列表已保存，共{len(skip_list)}个手机号")
    except Exception as e:
        print(f"保存跳过签到列表失败: {str(e)}")

def show_skip_list():
    """显示当前跳过签到列表"""
    skip_list = load_skip_list()
    if not skip_list:
        print("📝 当前跳过签到列表为空")
        return
    
    print("📋 当前跳过签到列表:")
    print("=" * 30)
    for i, phone in enumerate(skip_list, 1):
        masked_phone = phone[:3] + "****" + phone[-4:] if len(phone) >= 7 else phone
        print(f"{i:2d}. {masked_phone}")
    print("=" * 30)
    print(f"总计: {len(skip_list)} 个手机号")

def add_phone():
    """添加手机号到跳过签到列表"""
    phone = input("请输入要添加的手机号: ").strip()
    if not phone:
        print("❌ 手机号不能为空")
        return
    
    if not phone.isdigit() or len(phone) != 11:
        print("❌ 请输入正确的11位手机号")
        return
    
    skip_list = load_skip_list()
    if phone in skip_list:
        print(f"⚠️ 手机号 {phone[:3]}****{phone[-4:]} 已在跳过签到列表中")
        return
    
    skip_list.append(phone)
    save_skip_list(skip_list)
    print(f"✅ 已添加手机号 {phone[:3]}****{phone[-4:]} 到跳过签到列表")

def remove_phone():
    """从跳过签到列表中移除手机号"""
    skip_list = load_skip_list()
    if not skip_list:
        print("📝 跳过签到列表为空，无法移除")
        return
    
    show_skip_list()
    try:
        choice = input("\n请输入要移除的序号 (输入0取消): ").strip()
        if choice == "0":
            print("❌ 已取消操作")
            return
        
        index = int(choice) - 1
        if 0 <= index < len(skip_list):
            removed_phone = skip_list.pop(index)
            save_skip_list(skip_list)
            print(f"✅ 已移除手机号 {removed_phone[:3]}****{removed_phone[-4:]} 从跳过签到列表")
        else:
            print("❌ 序号无效")
    except ValueError:
        print("❌ 请输入有效的数字")

def clear_list():
    """清空跳过签到列表"""
    skip_list = load_skip_list()
    if not skip_list:
        print("📝 跳过签到列表已经为空")
        return
    
    confirm = input(f"⚠️ 确定要清空跳过签到列表吗？(共{len(skip_list)}个手机号) [y/N]: ").strip().lower()
    if confirm in ['y', 'yes']:
        save_skip_list([])
        print("✅ 跳过签到列表已清空")
    else:
        print("❌ 已取消操作")

def main():
    while True:
        print("\n" + "=" * 40)
        print("🔧 跳过签到列表管理工具")
        print("=" * 40)
        print("1. 查看跳过签到列表")
        print("2. 添加手机号到跳过签到列表")
        print("3. 从跳过签到列表移除手机号")
        print("4. 清空跳过签到列表")
        print("0. 退出")
        print("=" * 40)
        
        choice = input("请选择操作 (0-4): ").strip()
        
        if choice == "1":
            show_skip_list()
        elif choice == "2":
            add_phone()
        elif choice == "3":
            remove_phone()
        elif choice == "4":
            clear_list()
        elif choice == "0":
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请输入 0-4")

if __name__ == "__main__":
    main()
