#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新功能：今日完成检查和实时读取
"""

import os
from datetime import datetime

def test_today_completed():
    """测试今日完成功能"""
    print("=== 测试今日完成功能 ===")
    
    # 模拟手机号
    test_phone = "13800138000"
    
    # 导入函数
    import sys
    sys.path.append('.')
    from 顺丰1_1 import save_today_completed_phone, load_today_completed_list, clean_old_completed_files
    
    # 测试保存今日完成
    print(f"1. 保存测试手机号 {test_phone} 到今日完成列表")
    save_today_completed_phone(test_phone)
    
    # 测试加载今日完成列表
    print("2. 加载今日完成列表")
    completed_list = load_today_completed_list()
    print(f"   今日完成列表: {completed_list}")
    
    # 检查是否在列表中
    if test_phone in completed_list:
        print(f"✅ 手机号 {test_phone} 已在今日完成列表中")
    else:
        print(f"❌ 手机号 {test_phone} 不在今日完成列表中")
    
    # 查看生成的文件
    today = datetime.now().strftime('%Y-%m-%d')
    completed_file = f"今日已完成_{today}.txt"
    if os.path.exists(completed_file):
        print(f"3. 今日完成文件 {completed_file} 内容:")
        with open(completed_file, 'r', encoding='utf-8') as f:
            content = f.read()
            print(f"   {content.strip()}")
    
    print()

def test_skip_sign_realtime():
    """测试实时读取跳过签到列表"""
    print("=== 测试实时读取跳过签到功能 ===")
    
    # 导入函数
    import sys
    sys.path.append('.')
    from 顺丰1_1 import load_skip_sign_list, save_skip_sign_phone
    
    test_phone = "13900139000"
    
    # 测试保存跳过签到
    print(f"1. 保存测试手机号 {test_phone} 到跳过签到列表")
    save_skip_sign_phone(test_phone)
    
    # 测试实时读取
    print("2. 实时读取跳过签到列表")
    skip_list = load_skip_sign_list()
    print(f"   跳过签到列表: {skip_list}")
    
    # 检查是否在列表中
    if test_phone in skip_list:
        print(f"✅ 手机号 {test_phone} 已在跳过签到列表中")
    else:
        print(f"❌ 手机号 {test_phone} 不在跳过签到列表中")
    
    print()

def show_files():
    """显示相关文件"""
    print("=== 相关文件列表 ===")
    
    files_to_check = [
        "跳过签到.txt",
        f"今日已完成_{datetime.now().strftime('%Y-%m-%d')}.txt"
    ]
    
    for filename in files_to_check:
        if os.path.exists(filename):
            print(f"📁 {filename} (存在)")
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if content:
                    lines = content.split('\n')
                    print(f"   内容: {len(lines)} 行")
                    for i, line in enumerate(lines[:3], 1):  # 只显示前3行
                        print(f"   {i}. {line}")
                    if len(lines) > 3:
                        print(f"   ... 还有 {len(lines) - 3} 行")
                else:
                    print("   内容: 空文件")
        else:
            print(f"📁 {filename} (不存在)")
    
    print()

if __name__ == "__main__":
    print("🧪 开始测试新功能...")
    print()
    
    test_today_completed()
    test_skip_sign_realtime()
    show_files()
    
    print("✅ 测试完成！")
