import hashlib
import json
import os
import random
import time
from datetime import datetime
import requests
from requests.packages.urllib3.exceptions import InsecureRequestWarning

# 禁用安全请求警告
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

class BlackNumberChecker:
    def __init__(self, line_info, index):
        self.index = index + 1
        self.line_info = line_info  # 保存原始行信息

        # 解析格式：手机号----memberId----URL
        parts = line_info.split('----')
        if len(parts) >= 3:
            self.phone_raw = parts[0]
            self.member_id = parts[1]
            self.url = parts[2]
        else:
            raise ValueError(f"格式错误: {line_info}")
        
        self.s = requests.session()
        self.s.verify = False
        
        self.headers = {
            'Host': 'mcs-mimp-web.sf-express.com',
            'upgrade-insecure-requests': '1',
            'user-agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/50.0.2661.87 Safari/537.36',
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'accept-language': 'zh-CN,zh;q=0.9',
            'accept-encoding': 'gzip, deflate, br',
            'connection': 'keep-alive',
            'cache-control': 'max-age=0',
        }
        
        self.user_id = ''
        self.phone = ''
        self.mobile = ''
        self.is_black = False
        
    def login(self):
        """登录获取用户信息"""
        try:
            print(f'🔗 账号{self.index}开始登录检测')
            
            # 登录获取cookies
            ress = self.s.get(self.url, headers=self.headers, timeout=30)
            print(f'📊 账号{self.index}响应状态: {ress.status_code}')
            
            cookies = self.s.cookies.get_dict()
            print(f'🍪 账号{self.index}收到cookies数量: {len(cookies)}')
            
            # 获取关键信息
            self.user_id = cookies.get('_login_user_id_', '')
            self.phone = cookies.get('_login_mobile_', '')
            
            if self.phone:
                self.mobile = self.phone[:3] + "*" * 4 + self.phone[7:]
                print(f'👤 账号{self.index}:【{self.mobile}】登陆成功')
                return True
            else:
                # 如果没有获取到手机号，使用原始手机号
                if hasattr(self, 'phone_raw') and self.phone_raw:
                    self.phone = self.phone_raw
                    self.mobile = self.phone[:3] + "*" * 4 + self.phone[7:]
                    print(f'👤 账号{self.index}:【{self.mobile}】使用原始手机号')
                    return True
                print(f'❌ 账号{self.index}获取用户信息失败')
                return False
                
        except Exception as e:
            print(f'❌ 账号{self.index}登录异常: {str(e)}')
            return False
    
    def getSign(self):
        """生成签名"""
        timestamp = str(int(round(time.time() * 1000)))
        token = 'wwesldfs29aniversaryvdld29'
        sysCode = 'MCS-MIMP-CORE'
        data = f'token={token}&timestamp={timestamp}&sysCode={sysCode}'
        signature = hashlib.md5(data.encode()).hexdigest()
        
        sign_data = {
            'syscode': sysCode,
            'timestamp': timestamp,
            'signature': signature,
            'platform': 'WEIXIN',
            'channel': 'weixin'
        }
        self.headers.update(sign_data)
        return sign_data
    
    def check_black_number(self):
        """检测是否为黑号"""
        try:
            # 更新请求头
            self.getSign()
            self.headers.update({
                'Content-Type': 'application/json;charset=UTF-8',
                'Accept': 'application/json, text/plain, */*',
                'Origin': 'https://mcs-mimp-web.sf-express.com',
                'Referer': 'https://mcs-mimp-web.sf-express.com/inboxPresentInterMit',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Dest': 'empty',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) UnifiedPCWindowsWechat(0xf2540517) XWEB/13909 Flue'
            })
            
            # 请求检测接口
            url = 'https://mcs-mimp-web.sf-express.com/mcs-mimp/commonPost/~memberNonactivity~receiveExchangeIndexService~indexData'
            data = {}
            
            print(f'🔍 账号{self.index}开始检测黑号状态...')
            response = self.s.post(url, headers=self.headers, json=data, timeout=30)
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    
                    # 检查是否为黑号
                    if result.get('errorCode') == '100012' and '用户手机号校验未通过' in result.get('errorMessage', ''):
                        self.is_black = True
                        print(f'❌ 账号{self.index}【{self.mobile}】检测结果: 黑号 - {result.get("errorMessage")}')
                        return True, "黑号"
                    elif result.get('success') == True:
                        self.is_black = False
                        usable_honey = result.get('obj', {}).get('usableHoney', 0)
                        print(f'✅ 账号{self.index}【{self.mobile}】检测结果: 白号 - 可用丰蜜: {usable_honey}')
                        return True, "白号"
                    else:
                        print(f'⚠️ 账号{self.index}【{self.mobile}】检测结果: 未知状态 - {result.get("errorMessage", "无错误信息")}')
                        return True, "未知"
                        
                except json.JSONDecodeError:
                    print(f'❌ 账号{self.index}【{self.mobile}】响应解析失败: {response.text[:200]}')
                    return False, "解析失败"
            else:
                print(f'❌ 账号{self.index}【{self.mobile}】请求失败，状态码: {response.status_code}')
                return False, "请求失败"
                
        except Exception as e:
            print(f'❌ 账号{self.index}【{self.mobile}】检测异常: {str(e)}')
            return False, "检测异常"
    
    def check(self):
        """执行完整检测流程"""
        # 先登录
        if not self.login():
            return False, "登录失败", self.info
        
        # 等待一下避免请求过快
        time.sleep(random.uniform(1, 3))
        
        # 检测黑号
        success, status = self.check_black_number()
        if success:
            return True, status, self.line_info
        else:
            return False, status, self.line_info

def read_lines_from_file():
    """从登录成功_链接二.txt文件读取完整行信息"""
    file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "登录成功_链接二.txt")

    if not os.path.exists(file_path):
        print(f"❌ 未找到文件: {file_path}")
        return []

    lines = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue

                # 解析格式：手机号----memberId----URL
                parts = line.split('----')
                if len(parts) >= 3:
                    phone = parts[0]
                    member_id = parts[1]
                    url = parts[2]
                    lines.append(line)
                    print(f"📱 读取账号 {line_num}: {phone[:3]}****{phone[-4:]} -> 信息已获取")
                else:
                    print(f"⚠️ 第{line_num}行格式错误，跳过: {line}")

        print(f"✅ 成功从文件读取到 {len(lines)} 个账号")
        return lines

    except Exception as e:
        print(f"❌ 读取文件失败: {str(e)}")
        return []

def save_results(white_list, black_list, unknown_list, failed_list):
    """保存检测结果到文件，保持原始格式"""

    # 保存白号
    if white_list:
        with open("白号.txt", 'w', encoding='utf-8') as f:
            for line in white_list:
                f.write(f"{line}\n")
        print(f"✅ 白号已保存到: 白号.txt (共{len(white_list)}个)")

    # 保存黑号
    if black_list:
        with open("黑号.txt", 'w', encoding='utf-8') as f:
            for line in black_list:
                f.write(f"{line}\n")
        print(f"❌ 黑号已保存到: 黑号.txt (共{len(black_list)}个)")

    # 保存未知状态
    if unknown_list:
        with open("未知状态.txt", 'w', encoding='utf-8') as f:
            for line in unknown_list:
                f.write(f"{line}\n")
        print(f"⚠️ 未知状态已保存到: 未知状态.txt (共{len(unknown_list)}个)")

    # 保存检测失败
    if failed_list:
        with open("检测失败.txt", 'w', encoding='utf-8') as f:
            for line in failed_list:
                f.write(f"{line}\n")
        print(f"💥 检测失败已保存到: 检测失败.txt (共{len(failed_list)}个)")

def main():
    print("=" * 50)
    print("🔍 顺丰黑号检测工具")
    print("📝 功能: 检测账号是否为黑号并分类保存")
    print("=" * 50)
    
    # 读取完整行信息
    lines = read_lines_from_file()
    if not lines:
        print("❌ 未找到有效的账号数据")
        return

    print(f"📊 开始检测 {len(lines)} 个账号...")
    print("=" * 50)

    # 分类列表
    white_list = []  # 白号
    black_list = []  # 黑号
    unknown_list = []  # 未知状态
    failed_list = []  # 检测失败

    # 逐个检测
    for index, line in enumerate(lines):
        try:
            checker = BlackNumberChecker(line, index)
            success, status, original_line = checker.check()

            if success:
                if status == "白号":
                    white_list.append(original_line)
                elif status == "黑号":
                    black_list.append(original_line)
                else:  # 未知状态
                    unknown_list.append(original_line)
            else:
                failed_list.append(original_line)

            # 每检测完一个账号后稍作等待
            if index < len(lines) - 1:  # 最后一个不需要等待
                time.sleep(random.uniform(2, 5))

        except Exception as e:
            print(f"❌ 账号{index + 1}检测出现异常: {str(e)}")
            failed_list.append(line)
    
    # 输出统计结果
    print("=" * 50)
    print("📊 检测完成，统计结果:")
    print(f"✅ 白号: {len(white_list)} 个")
    print(f"❌ 黑号: {len(black_list)} 个")
    print(f"⚠️ 未知状态: {len(unknown_list)} 个")
    print(f"💥 检测失败: {len(failed_list)} 个")
    print(f"📱 总计: {len(lines)} 个")
    print("=" * 50)
    
    # 保存结果到文件
    save_results(white_list, black_list, unknown_list, failed_list)
    
    print("🎉 黑号检测完成！")

if __name__ == '__main__':
    main()
