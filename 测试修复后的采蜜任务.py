"""
测试修复后的采蜜任务获取
"""
import hashlib
import json
import time
import requests
from requests.packages.urllib3.exceptions import InsecureRequestWarning

# 禁用安全请求警告
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

def test_fixed_honey_task():
    """测试修复后的采蜜任务获取"""
    test_url = "https://mcs-mimp-web.sf-express.com/mcs-mimp/share/weChat/shareGiftReceiveRedirect?source=CX&scene=6&openId=6tB15T6k0ZvyroDMcHJm6XG4WdZ0V1SUTxA9ZutcCj0DdjSr%2F6X0WoiZtgGzs7sG&memId=d97Q3fSsQut3wngIwPDenDojXNFwFq75bZ6HcYnGPBwDdjSr%2F6X0WoiZtgGzs7sG&memNo=6tB15T6k0ZvyroDMcHJm6XG4WdZ0V1SUTxA9ZutcCj0DdjSr%2F6X0WoiZtgGzs7sG&mobile=rLfLkJ2OpnPOH0mB82gAew%3D%3D&bizCode=619%40%40Mmh0VWdPUGxrMExNZmlBWDF1SVJHSDhOcHBjNlpFL3pIQ0cyTWpzWEhqRT0%3D&mediaCode=miniBd&cx-at-sign=C71B1E93F9DAB3B12A199F69FD2B2585CF1D33519A1CA5240DFAAE0CF8799DCA&cx-at-ts=1751464944&cx-at-nonce=NDV4jmNgQHG775G2nj7Cj"
    
    print("=" * 60)
    print("测试修复后的采蜜任务获取")
    print("=" * 60)
    
    # 第一步：登录获取cookies
    login_headers = {
        'Host': 'mcs-mimp-web.sf-express.com',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090551) XWEB/6945 Flue',
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'sec-fetch-site': 'none',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-user': '?1',
        'sec-fetch-dest': 'document',
        'accept-language': 'zh-CN,zh',
        'platform': 'MINI_PROGRAM',
    }
    
    session = requests.Session()
    session.verify = False
    
    try:
        print(f"步骤1: 登录获取cookies...")
        login_response = session.get(test_url, headers=login_headers, timeout=30)
        
        cookies = session.cookies.get_dict()
        print(f"登录状态: {login_response.status_code}")
        print(f"Cookies数量: {len(cookies)}")
        
        phone = cookies.get('_login_mobile_', '')
        user_id = cookies.get('_login_user_id_', '')
        
        if not (phone and user_id):
            print(f"❌ 登录失败，无法获取关键cookies")
            return False
        
        print(f"✅ 登录成功")
        print(f"手机号: {phone}")
        print(f"用户ID: {user_id}")
        
        # 第二步：使用修复后的请求头获取采蜜任务
        print(f"\n步骤2: 获取采蜜任务列表...")
        
        # 生成签名
        timestamp = str(int(round(time.time() * 1000)))
        token = 'wwesldfs29aniversaryvdld29'
        sysCode = 'MCS-MIMP-CORE'
        data = f'token={token}&timestamp={timestamp}&sysCode={sysCode}'
        signature = hashlib.md5(data.encode()).hexdigest()
        
        # 使用修复后的请求头（基于你的手动请求）
        honey_headers = {
            'Host': 'mcs-mimp-web.sf-express.com',
            'Connection': 'keep-alive',
            'Content-Type': 'application/json;charset=UTF-8',
            'Accept': 'application/json, text/plain, */*',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) UnifiedPCWindowsWechat(0xf2540517) XWEB/13909 Flue',
            'sysCode': sysCode,
            'timestamp': timestamp,
            'signature': signature,
            'platform': 'WEIXIN',
            'channel': 'weixin',
            'Origin': 'https://mcs-mimp-web.sf-express.com',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': 'https://mcs-mimp-web.sf-express.com/inboxPresent',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9'
        }
        
        honey_url = 'https://mcs-mimp-web.sf-express.com/mcs-mimp/commonPost/~memberNonactivity~receiveExchangeIndexService~taskDetail'
        json_data = {}
        
        honey_response = session.post(honey_url, headers=honey_headers, json=json_data, timeout=30)
        
        print(f"采蜜任务响应状态: {honey_response.status_code}")
        print(f"采蜜任务响应内容: {honey_response.text[:200]}...")
        
        if honey_response.status_code == 200:
            try:
                result = honey_response.json()
                print(f"\n采蜜任务完整响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
                
                if result.get('success') == True:
                    print(f"✅ 采蜜任务获取成功")
                    
                    obj = result.get('obj', {})
                    task_list = obj.get('list', [])
                    level = obj.get('level', '未知')
                    
                    print(f"账号等级: {level}")
                    print(f"任务数量: {len(task_list)}")
                    
                    if task_list:
                        print(f"\n任务详情:")
                        for i, task in enumerate(task_list, 1):
                            task_type = task.get('taskType', '未知')
                            status = task.get('status', '未知')
                            count = task.get('count', 0)
                            task_code = task.get('taskCode', '')
                            
                            status_text = {
                                1: '可执行',
                                2: '待领取',
                                3: '已完成'
                            }.get(status, f'状态{status}')
                            
                            print(f"  {i}. {task_type}")
                            print(f"     状态: {status_text}")
                            print(f"     计数: {count}")
                            if task_code:
                                print(f"     任务码: {task_code}")
                            print()
                        
                        return True
                    else:
                        print(f"⚠️ 任务列表为空")
                        return True  # 成功获取，但列表为空
                else:
                    error_msg = result.get('errorMessage', '无错误信息')
                    print(f"❌ 采蜜任务获取失败: {error_msg}")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                return False
        else:
            print(f"❌ 采蜜任务请求失败，状态码: {honey_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_headers():
    """对比请求头差异"""
    print(f"\n" + "=" * 60)
    print("请求头对比分析")
    print("=" * 60)
    
    print(f"手动请求成功的关键请求头:")
    print(f"- platform: WEIXIN (不是 MINI_PROGRAM)")
    print(f"- channel: weixin (不是 wxwdsj)")
    print(f"- Content-Type: application/json;charset=UTF-8")
    print(f"- Accept: application/json, text/plain, */*")
    print(f"- Origin: https://mcs-mimp-web.sf-express.com")
    print(f"- Referer: https://mcs-mimp-web.sf-express.com/inboxPresent")
    print(f"- Sec-Fetch-Site: same-origin")
    print(f"- Sec-Fetch-Mode: cors")
    print(f"- Sec-Fetch-Dest: empty")
    
    print(f"\n老版本代码的问题:")
    print(f"- 使用了错误的 platform 值")
    print(f"- 使用了错误的 channel 值")
    print(f"- 缺少必要的 Origin 和 Referer")
    print(f"- 缺少正确的 Content-Type")

if __name__ == "__main__":
    try:
        # 测试修复后的采蜜任务获取
        success = test_fixed_honey_task()
        
        # 对比分析
        compare_headers()
        
        print(f"\n" + "=" * 60)
        print("测试结果")
        print("=" * 60)
        
        if success:
            print(f"✅ 修复成功！现在可以正确获取采蜜任务列表")
            print(f"\n关键修复点:")
            print(f"1. platform: 'WEIXIN' (不是 'MINI_PROGRAM')")
            print(f"2. channel: 'weixin' (不是 'wxwdsj')")
            print(f"3. 添加了正确的 Origin 和 Referer")
            print(f"4. 添加了正确的 Content-Type")
        else:
            print(f"❌ 修复失败，需要进一步调试")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
