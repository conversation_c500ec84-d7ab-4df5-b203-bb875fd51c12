"""
测试从登录成功_链接二.txt文件读取URL的功能
"""
import os

def read_urls_from_file():
    """从登录成功_链接二.txt文件读取URL列表，返回URL字符串（用&连接）"""
    file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "登录成功_链接二.txt")

    if not os.path.exists(file_path):
        print(f"❌ 未找到文件: {file_path}")
        return ""

    urls = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue

                # 解析格式：手机号----memberId----URL
                parts = line.split('----')
                if len(parts) >= 3:
                    phone = parts[0]
                    member_id = parts[1]
                    url = parts[2]
                    urls.append(url)
                    print(f"📱 读取账号 {line_num}: {phone[:3]}****{phone[-4:]} -> URL已获取")
                else:
                    print(f"⚠️ 第{line_num}行格式错误，跳过: {line}")

        print(f"✅ 成功从文件读取到 {len(urls)} 个账号")
        # 用特殊分隔符连接所有URL，避免与URL中的&冲突
        return "||||".join(urls)

    except Exception as e:
        print(f"❌ 读取文件失败: {str(e)}")
        return ""

def test_file_reading():
    """测试文件读取功能"""
    print("=" * 60)
    print("测试从登录成功_链接二.txt文件读取URL")
    print("=" * 60)
    
    # 检查文件是否存在
    file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "登录成功_链接二.txt")
    print(f"文件路径: {file_path}")
    print(f"文件存在: {'✅' if os.path.exists(file_path) else '❌'}")
    
    if os.path.exists(file_path):
        # 显示文件大小和行数
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                print(f"文件大小: {os.path.getsize(file_path)} 字节")
                print(f"总行数: {len(lines)}")
                print(f"非空行数: {len([line for line in lines if line.strip()])}")
        except Exception as e:
            print(f"读取文件信息失败: {e}")
    
    print("\n" + "-" * 40)
    print("开始解析文件内容:")
    print("-" * 40)
    
    # 读取URL字符串
    token = read_urls_from_file()

    print("\n" + "-" * 40)
    print("解析结果统计:")
    print("-" * 40)

    if token:
        # 分割URL - 根据分隔符类型
        if "||||" in token:
            urls = token.split('||||')
        else:
            urls = token.split('&')
        urls = [url.strip() for url in urls if url.strip()]

        print(f"成功解析的URL数量: {len(urls)}")
        print(f"连接后的字符串长度: {len(token)} 字符")

        if urls:
            print(f"第一个URL示例: {urls[0][:80]}...")
            print(f"最后一个URL示例: {urls[-1][:80]}...")

            # 检查URL格式
            valid_urls = 0
            for url in urls:
                if url.startswith('https://') and 'sf-express.com' in url:
                    valid_urls += 1

            print(f"有效的顺丰URL数量: {valid_urls}")
            print(f"URL有效率: {valid_urls/len(urls)*100:.1f}%")
    else:
        print("未读取到任何URL")
        urls = []

    return urls

def simulate_main_function():
    """模拟主函数的执行流程"""
    print("\n" + "=" * 60)
    print("模拟主函数执行流程")
    print("=" * 60)
    
    # 模拟原来的环境变量读取方式
    print("原方式 - 从环境变量读取:")
    env_value = os.getenv('sfsyUrl')
    if env_value:
        print(f"✅ 环境变量存在: {len(env_value)} 字符")
    else:
        print("❌ 环境变量不存在")
    
    print("\n新方式 - 从文件读取:")
    # 模拟主函数的逻辑
    token = read_urls_from_file()
    if not token:
        print("❌ 未能从文件读取到有效的URL")
        return False

    # 分割URL，模拟环境变量处理方式
    if "||||" in token:
        tokens = token.split('||||')
    else:
        tokens = token.split('&')
    tokens = [t.strip() for t in tokens if t.strip()]

    if len(tokens) == 0:
        print("❌ 数据为空或格式错误")
        return False
    
    print(f"\n模拟脚本启动信息:")
    print(f"==================================")
    print(f"🎉 呆呆粉丝后援会：996374999")
    print(f"🚚 顺丰速运脚本 v2025.06.23")
    print(f"📁 数据来源: 登录成功_链接二.txt")
    print(f"📱 共获取到{len(tokens)}个账号")
    print(f"🎯 兑换配置:")
    print(f"  └ 兑换区间: 23-15 → 23元 > 20元 > 15元")
    print(f"  └ 强制兑换: 关闭")
    print(f"  └ 最大次数: 3")
    print(f"😣 修改By:呆呆呆呆")
    print(f"==================================")
    
    print(f"\n准备处理 {len(tokens)} 个账号...")
    for index, infos in enumerate(tokens[:3]):  # 只显示前3个作为示例
        print(f"账号 {index + 1}: URL长度 {len(infos)} 字符")

    if len(tokens) > 3:
        print(f"... 还有 {len(tokens) - 3} 个账号")
    
    return True

if __name__ == "__main__":
    # 测试文件读取
    urls = test_file_reading()
    
    # 模拟主函数
    simulate_main_function()
    
    print(f"\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60)
    print("修改说明:")
    print("1. ✅ 已将数据源从环境变量改为文件读取")
    print("2. ✅ 支持解析 手机号----memberId----URL 格式")
    print("3. ✅ 增加了错误处理和详细的日志输出")
    print("4. ✅ 保持了原有的功能逻辑不变")
    print("5. ✅ 文件路径使用相对路径，自动定位到脚本同目录")
