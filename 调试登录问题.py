"""
调试顺丰登录问题 - 分析"获取用户信息失败"的原因
"""
import requests
from urllib.parse import unquote
import os

def debug_login():
    """调试登录过程"""
    print("=" * 60)
    print("调试顺丰登录问题")
    print("=" * 60)
    
    # 读取测试URL
    file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "测试.txt")
    
    if not os.path.exists(file_path):
        print(f"❌ 未找到文件: {file_path}")
        return
    
    with open(file_path, 'r', encoding='utf-8') as f:
        line = f.readline().strip()
    
    if not line:
        print("❌ 文件为空")
        return
    
    # 解析格式：手机号----memberId----URL
    parts = line.split('----')
    if len(parts) < 3:
        print(f"❌ 格式错误: {line}")
        return
    
    phone = parts[0]
    member_id = parts[1]
    url = parts[2]
    
    print(f"📱 手机号: {phone}")
    print(f"🆔 Member ID: {member_id}")
    print(f"🔗 URL长度: {len(url)} 字符")
    print(f"🔗 URL预览: {url[:100]}...")
    
    # 设置请求头（模拟原代码）
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'sec-fetch-site': 'none',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-user': '?1',
        'sec-fetch-dest': 'document',
        'accept-language': 'zh-CN,zh',
        'platform': 'MINI_PROGRAM',
    }
    
    print(f"\n📡 开始请求...")
    
    try:
        # 创建session
        session = requests.Session()
        
        # 解码URL
        decoded_url = unquote(url)
        print(f"🔓 解码后URL长度: {len(decoded_url)} 字符")
        print(f"🔓 解码后URL预览: {decoded_url[:100]}...")
        
        # 发送请求
        response = session.get(decoded_url, headers=headers, timeout=30)
        
        print(f"\n📊 响应信息:")
        print(f"状态码: {response.status_code}")
        print(f"响应头数量: {len(response.headers)}")
        print(f"响应内容长度: {len(response.text)} 字符")
        
        # 检查cookies
        cookies = session.cookies.get_dict()
        print(f"\n🍪 Cookies信息:")
        print(f"Cookies数量: {len(cookies)}")
        
        for key, value in cookies.items():
            print(f"  {key}: {value[:50]}..." if len(value) > 50 else f"  {key}: {value}")
        
        # 检查关键cookies
        user_id = cookies.get('_login_user_id_', '')
        phone_cookie = cookies.get('_login_mobile_', '')
        
        print(f"\n🔍 关键信息检查:")
        print(f"_login_user_id_: {'✅ 存在' if user_id else '❌ 不存在'} - {user_id}")
        print(f"_login_mobile_: {'✅ 存在' if phone_cookie else '❌ 不存在'} - {phone_cookie}")
        
        if phone_cookie:
            mobile_display = phone_cookie[:3] + "*" * 4 + phone_cookie[7:] if len(phone_cookie) >= 7 else phone_cookie
            print(f"📱 手机号显示: {mobile_display}")
            print(f"✅ 登录成功")
        else:
            print(f"❌ 获取用户信息失败 - 无法从cookies中获取手机号")
            
            # 分析可能的原因
            print(f"\n🔍 问题分析:")
            if response.status_code != 200:
                print(f"  - HTTP状态码异常: {response.status_code}")
            
            if len(cookies) == 0:
                print(f"  - 没有收到任何cookies")
            else:
                print(f"  - 收到了cookies但缺少关键字段")
                print(f"  - 可能的原因：")
                print(f"    1. URL已过期")
                print(f"    2. 顺丰服务器cookies字段名称变化")
                print(f"    3. 需要额外的验证步骤")
            
            # 检查响应内容中是否有错误信息
            if 'error' in response.text.lower() or '错误' in response.text:
                print(f"  - 响应内容包含错误信息")
            
            # 检查是否需要重定向
            if response.history:
                print(f"  - 发生了 {len(response.history)} 次重定向")
                for i, resp in enumerate(response.history):
                    print(f"    重定向 {i+1}: {resp.status_code} -> {resp.url[:80]}...")
        
        # 保存响应内容用于分析
        with open('debug_response.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print(f"\n💾 响应内容已保存到 debug_response.html")
        
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        import traceback
        traceback.print_exc()

def check_url_format():
    """检查URL格式"""
    print(f"\n" + "=" * 60)
    print("检查URL格式")
    print("=" * 60)
    
    file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "测试.txt")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        line = f.readline().strip()
    
    parts = line.split('----')
    url = parts[2]
    
    print(f"URL分析:")
    print(f"长度: {len(url)}")
    print(f"协议: {'✅ HTTPS' if url.startswith('https://') else '❌ 非HTTPS'}")
    print(f"域名: {'✅ 顺丰' if 'sf-express.com' in url else '❌ 非顺丰域名'}")
    
    # 检查URL参数
    if '?' in url:
        base_url, params = url.split('?', 1)
        param_pairs = params.split('&')
        print(f"参数数量: {len(param_pairs)}")
        
        key_params = ['openId', 'memId', 'mobile', 'cx-at-sign', 'cx-at-ts', 'cx-at-nonce']
        for key in key_params:
            found = any(p.startswith(f'{key}=') for p in param_pairs)
            print(f"  {key}: {'✅' if found else '❌'}")

if __name__ == "__main__":
    try:
        check_url_format()
        debug_login()
        
        print(f"\n" + "=" * 60)
        print("调试完成！")
        print("=" * 60)
        print("可能的解决方案:")
        print("1. 检查URL是否过期，重新获取最新的登录链接")
        print("2. 检查网络连接是否正常")
        print("3. 检查顺丰服务器是否有变化")
        print("4. 尝试使用不同的User-Agent")
        
    except Exception as e:
        print(f"调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
