"""
测试修复后的登录逻辑（使用老版本的方式）
"""
import requests
import os
import time

def Log(message):
    """日志输出函数"""
    print(f"[{time.strftime('%H:%M:%S')}] {message}")

def test_old_version_login():
    """测试老版本的登录方式"""
    print("=" * 60)
    print("测试老版本登录方式")
    print("=" * 60)
    
    # 从文件读取URL
    file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "测试.txt")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        line = f.readline().strip()
    
    parts = line.split('----')
    expected_phone = parts[0]
    expected_user_id = parts[1]
    url = parts[2]
    
    print(f"📱 期望手机号: {expected_phone}")
    print(f"🆔 期望用户ID: {expected_user_id}")
    
    # 使用老版本的headers
    headers = {
        'Host': 'mcs-mimp-web.sf-express.com',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090551) XWEB/6945 Flue',
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'sec-fetch-site': 'none',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-user': '?1',
        'sec-fetch-dest': 'document',
        'accept-language': 'zh-CN,zh',
        'platform': 'MINI_PROGRAM',
    }
    
    try:
        session = requests.Session()
        session.verify = False  # 老版本设置
        
        Log(f'👉 账号1正在登录...')
        
        # 完全按照老版本的方式：直接使用原始URL，允许重定向
        ress = session.get(url, headers=headers)
        
        Log(f'📊 响应状态: {ress.status_code}')
        Log(f'📄 内容长度: {len(ress.text)}')
        
        # 获取用户信息（老版本方式）
        user_id = session.cookies.get_dict().get('_login_user_id_', '')
        phone = session.cookies.get_dict().get('_login_mobile_', '')
        mobile = phone[:3] + "*" * 4 + phone[7:] if phone else ''
        
        cookies = session.cookies.get_dict()
        Log(f'🍪 收到cookies数量: {len(cookies)}')
        
        if cookies:
            cookie_string = "; ".join([f"{k}={v}" for k, v in cookies.items()])
            Log(f'🍪 Cookies内容: {cookie_string}')
        
        if phone:
            Log(f'👤 账号1:【{mobile}】登陆成功')
            Log(f'🆔 用户ID: {user_id}')
            
            # 验证数据正确性
            phone_match = phone == expected_phone
            user_id_match = user_id == expected_user_id
            
            Log(f'🔍 手机号匹配: {"✅" if phone_match else "❌"}')
            Log(f'🔍 用户ID匹配: {"✅" if user_id_match else "❌"}')
            
            return True
        else:
            Log(f'❌ 账号1获取用户信息失败')
            return False
            
    except Exception as e:
        Log(f'❌ 登录异常: {str(e)}')
        return False

def test_new_version_fixed():
    """测试修复后的新版本登录方式"""
    print(f"\n" + "=" * 60)
    print("测试修复后的新版本登录方式")
    print("=" * 60)
    
    # 模拟修复后的RUN类
    class MockRUN:
        def __init__(self, url, index):
            self.index = index
            self.s = requests.Session()
            self.s.verify = False
            
            self.headers = {
                'Host': 'mcs-mimp-web.sf-express.com',
                'upgrade-insecure-requests': '1',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090551) XWEB/6945 Flue',
                'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
                'sec-fetch-site': 'none',
                'sec-fetch-mode': 'navigate',
                'sec-fetch-user': '?1',
                'sec-fetch-dest': 'document',
                'accept-language': 'zh-CN,zh',
                'platform': 'MINI_PROGRAM',
            }
            
            self.login_res = self.login(url)
        
        def login(self, sfurl):
            try:
                # 直接使用原始URL，不进行解码处理（与老版本保持一致）
                Log(f'🔗 账号{self.index}开始登录')
                
                # 使用与老版本相同的请求方式：允许重定向，不解码URL
                ress = self.s.get(sfurl, headers=self.headers, timeout=30)
                
                Log(f'📊 账号{self.index}响应状态: {ress.status_code}')
                
                cookies = self.s.cookies.get_dict()
                Log(f'🍪 账号{self.index}收到cookies数量: {len(cookies)}')
                
                # 获取关键信息
                self.user_id = cookies.get('_login_user_id_', '')
                self.phone = cookies.get('_login_mobile_', '')
                
                # 调试：显示所有cookies
                if len(cookies) > 0:
                    cookie_list = [f"{k}={v}" for k, v in cookies.items()]
                    Log(f'🍪 账号{self.index}cookies: {"; ".join(cookie_list)}')
                
                self.mobile = self.phone[:3] + "*" * 4 + self.phone[7:] if self.phone else ''
                
                if self.phone:
                    Log(f'👤 账号{self.index}:【{self.mobile}】登陆成功')
                    Log(f'🆔 账号{self.index}用户ID: {self.user_id}')
                    return True
                else:
                    Log(f'❌ 账号{self.index}获取用户信息失败')
                    Log(f'🔍 账号{self.index}状态码: {ress.status_code}, cookies数量: {len(cookies)}')
                    
                    # 显示实际收到的cookies用于调试
                    if cookies:
                        Log(f'🔍 账号{self.index}实际cookies: {list(cookies.keys())}')
                    
                    return False
            except Exception as e:
                Log(f'❌ 账号{self.index}登录异常: {str(e)}')
                return False
    
    # 测试修复后的登录
    file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "测试.txt")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        line = f.readline().strip()
    
    parts = line.split('----')
    url = parts[2]
    
    mock_run = MockRUN(url, 1)
    
    return mock_run.login_res

def compare_methods():
    """对比不同方法的效果"""
    print(f"\n" + "=" * 60)
    print("对比不同登录方法")
    print("=" * 60)
    
    file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "测试.txt")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        line = f.readline().strip()
    
    parts = line.split('----')
    url = parts[2]
    
    headers = {
        'Host': 'mcs-mimp-web.sf-express.com',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090551) XWEB/6945 Flue',
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'sec-fetch-site': 'none',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-user': '?1',
        'sec-fetch-dest': 'document',
        'accept-language': 'zh-CN,zh',
        'platform': 'MINI_PROGRAM',
    }
    
    methods = [
        {
            'name': '老版本方式（原始URL + 允许重定向）',
            'url': url,
            'allow_redirects': True
        },
        {
            'name': '错误方式（解码URL + 允许重定向）',
            'url': requests.utils.unquote(url),
            'allow_redirects': True
        },
        {
            'name': '错误方式（解码URL + 禁止重定向）',
            'url': requests.utils.unquote(url),
            'allow_redirects': False
        }
    ]
    
    results = []
    
    for method in methods:
        print(f"\n测试: {method['name']}")
        print("-" * 40)
        
        try:
            session = requests.Session()
            session.verify = False
            
            response = session.get(
                method['url'], 
                headers=headers, 
                timeout=30,
                allow_redirects=method['allow_redirects']
            )
            
            cookies = session.cookies.get_dict()
            phone = cookies.get('_login_mobile_', '')
            user_id = cookies.get('_login_user_id_', '')
            
            success = bool(phone and user_id)
            results.append({
                'name': method['name'],
                'success': success,
                'cookies_count': len(cookies),
                'phone': phone,
                'user_id': user_id
            })
            
            print(f"状态: {'✅ 成功' if success else '❌ 失败'}")
            print(f"Cookies数量: {len(cookies)}")
            if success:
                print(f"手机号: {phone}")
                print(f"用户ID: {user_id[:20]}...")
            else:
                print(f"收到的cookies: {list(cookies.keys())}")
                
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            results.append({
                'name': method['name'],
                'success': False,
                'cookies_count': 0,
                'phone': '',
                'user_id': ''
            })
    
    # 总结
    print(f"\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    for result in results:
        status = "✅ 成功" if result['success'] else "❌ 失败"
        print(f"{result['name']}: {status}")
    
    successful_methods = [r for r in results if r['success']]
    if successful_methods:
        print(f"\n🎉 成功的方法: {len(successful_methods)} 个")
        print("建议使用老版本的方式：原始URL + 允许重定向")
    else:
        print(f"\n❌ 所有方法都失败")

if __name__ == "__main__":
    try:
        # 测试老版本方式
        success1 = test_old_version_login()
        
        # 测试修复后的新版本
        success2 = test_new_version_fixed()
        
        # 对比不同方法
        compare_methods()
        
        print(f"\n" + "=" * 60)
        print("测试总结")
        print("=" * 60)
        print(f"老版本方式: {'✅ 成功' if success1 else '❌ 失败'}")
        print(f"修复后新版本: {'✅ 成功' if success2 else '❌ 失败'}")
        
        if success1 or success2:
            print(f"\n🎉 修复成功！")
            print(f"关键修复点:")
            print(f"1. 使用原始URL，不进行unquote解码")
            print(f"2. 允许重定向（不使用allow_redirects=False）")
            print(f"3. 保持与老版本相同的请求方式")
        else:
            print(f"\n❌ 修复失败，需要进一步调试")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
