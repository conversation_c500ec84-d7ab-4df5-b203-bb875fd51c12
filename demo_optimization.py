#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示黑号检测优化效果
"""

def demo_black_number_conditions():
    """演示新的黑号识别条件"""
    print("🔍 黑号识别条件演示")
    print("=" * 50)
    
    # 示例响应数据
    examples = [
        {
            "title": "原有黑号条件",
            "data": {
                "errorCode": "100012",
                "errorMessage": "用户手机号校验未通过",
                "success": False
            }
        },
        {
            "title": "新增黑号条件 - 暂无参与资格",
            "data": {
                "date": "2025-07-04 12:34:53",
                "errorCode": "100012", 
                "errorMessage": "暂无参与资格",
                "notSuccess": True,
                "success": False
            }
        },
        {
            "title": "白号条件",
            "data": {
                "success": True,
                "obj": {
                    "usableHoney": 1500
                }
            }
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['title']}:")
        print(f"   响应数据: {example['data']}")
        
        # 应用检测逻辑
        result = example['data']
        error_code = result.get('errorCode')
        error_message = result.get('errorMessage', '')
        not_success = result.get('notSuccess', False)
        success = result.get('success', False)
        
        # 黑号判断条件
        is_black_number = (
            (error_code == '100012' and '用户手机号校验未通过' in error_message) or
            (error_code == '100012' and '暂无参与资格' in error_message) or
            (not_success == True and success == False and error_code == '100012')
        )
        
        if is_black_number:
            print(f"   检测结果: ❌ 黑号")
        elif success == True:
            usable_honey = result.get('obj', {}).get('usableHoney', 0)
            print(f"   检测结果: ✅ 白号 (可用丰蜜: {usable_honey})")
        else:
            print(f"   检测结果: ⚠️ 未知状态")

def demo_white_list_optimization():
    """演示白号优化效果"""
    print("\n\n🚀 白号优化效果演示")
    print("=" * 50)
    
    # 模拟账号数据
    all_accounts = [
        "***********----member1----https://example.com/1",
        "***********----member2----https://example.com/2", 
        "***********----member3----https://example.com/3",
        "***********----member4----https://example.com/4",
        "***********----member5----https://example.com/5"
    ]
    
    # 模拟已知白号
    known_white_phones = {"***********", "***********", "***********"}
    
    print(f"📊 总账号数: {len(all_accounts)}")
    print(f"📋 已知白号: {len(known_white_phones)}")
    
    print("\n处理过程:")
    skipped_count = 0
    checked_count = 0
    
    for i, account in enumerate(all_accounts, 1):
        phone = account.split('----')[0]
        mobile_display = phone[:3] + "*" * 4 + phone[7:]
        
        if phone in known_white_phones:
            skipped_count += 1
            print(f"   {i}. 【{mobile_display}】⚡ 跳过检测 (已知白号)")
        else:
            checked_count += 1
            print(f"   {i}. 【{mobile_display}】🔍 需要检测")
    
    print(f"\n优化效果:")
    print(f"   ⚡ 跳过检测: {skipped_count} 个")
    print(f"   🔍 实际检测: {checked_count} 个")
    print(f"   ⏱️ 节省时间: 约 {skipped_count * 3} 秒")
    print(f"   📈 效率提升: {skipped_count/len(all_accounts)*100:.1f}%")

def demo_usage_instructions():
    """使用说明"""
    print("\n\n📖 使用说明")
    print("=" * 50)
    
    instructions = [
        "1. 准备文件:",
        "   - 登录成功_白_链接二.txt (必需)",
        "   - 白号.txt (可选，用于优化)",
        "",
        "2. 运行检测:",
        "   python 剔除黑号.py",
        "",
        "3. 输出文件:",
        "   - 白号.txt (所有白号)",
        "   - 黑号.txt (所有黑号)", 
        "   - 未知状态.txt (未知状态)",
        "   - 检测失败.txt (检测失败)",
        "",
        "4. 优化特性:",
        "   - 自动跳过已知白号",
        "   - 扩展黑号识别条件",
        "   - 显示优化统计信息"
    ]
    
    for instruction in instructions:
        print(instruction)

if __name__ == "__main__":
    demo_black_number_conditions()
    demo_white_list_optimization() 
    demo_usage_instructions()
    
    print("\n" + "=" * 50)
    print("✅ 演示完成！")
