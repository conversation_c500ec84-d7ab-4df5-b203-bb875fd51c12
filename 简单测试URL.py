"""
简单测试指定URL
"""
import requests
import time

def test_url():
    """测试指定URL"""
    test_url = "https://mcs-mimp-web.sf-express.com/mcs-mimp/share/weChat/shareGiftReceiveRedirect?source=CX&scene=6&openId=6tB15T6k0ZvyroDMcHJm6XG4WdZ0V1SUTxA9ZutcCj0DdjSr%2F6X0WoiZtgGzs7sG&memId=d97Q3fSsQut3wngIwPDenDojXNFwFq75bZ6HcYnGPBwDdjSr%2F6X0WoiZtgGzs7sG&memNo=6tB15T6k0ZvyroDMcHJm6XG4WdZ0V1SUTxA9ZutcCj0DdjSr%2F6X0WoiZtgGzs7sG&mobile=rLfLkJ2OpnPOH0mB82gAew%3D%3D&bizCode=619%40%40Mmh0VWdPUGxrMExNZmlBWDF1SVJHSDhOcHBjNlpFL3pIQ0cyTWpzWEhqRT0%3D&mediaCode=miniBd&cx-at-sign=C71B1E93F9DAB3B12A199F69FD2B2585CF1D33519A1CA5240DFAAE0CF8799DCA&cx-at-ts=1751464944&cx-at-nonce=NDV4jmNgQHG775G2nj7Cj"
    
    print("=" * 60)
    print("测试指定URL")
    print("=" * 60)
    print(f"URL: {test_url[:80]}...")
    
    # 老版本方式
    headers = {
        'Host': 'mcs-mimp-web.sf-express.com',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090551) XWEB/6945 Flue',
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'sec-fetch-site': 'none',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-user': '?1',
        'sec-fetch-dest': 'document',
        'accept-language': 'zh-CN,zh',
        'platform': 'MINI_PROGRAM',
    }
    
    try:
        session = requests.Session()
        session.verify = False
        
        print(f"发送请求...")
        response = session.get(test_url, headers=headers, timeout=30)
        
        print(f"响应状态: {response.status_code}")
        print(f"响应内容长度: {len(response.text)}")
        
        cookies = session.cookies.get_dict()
        print(f"Cookies数量: {len(cookies)}")
        
        if cookies:
            print(f"Cookies列表: {list(cookies.keys())}")
            
            # 检查关键cookies
            phone = cookies.get('_login_mobile_', '')
            user_id = cookies.get('_login_user_id_', '')
            
            if phone and user_id:
                mobile = phone[:3] + "*" * 4 + phone[7:] if len(phone) >= 7 else phone
                print(f"✅ 登录成功")
                print(f"手机号: {phone}")
                print(f"显示手机号: {mobile}")
                print(f"用户ID: {user_id}")
                return True, session
            else:
                print(f"❌ 缺少关键cookies")
                print(f"_login_mobile_: {phone}")
                print(f"_login_user_id_: {user_id}")
                return False, None
        else:
            print(f"❌ 没有收到任何cookies")
            return False, None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False, None

def test_honey_task(session):
    """测试采蜜任务"""
    if not session:
        print("❌ 没有有效的session，跳过采蜜任务测试")
        return False
    
    print(f"\n" + "=" * 60)
    print("测试采蜜任务")
    print("=" * 60)
    
    import hashlib
    import json
    
    # 准备请求头
    headers = session.headers.copy()
    headers['channel'] = 'wxwdsj'
    
    # 添加签名
    timestamp = str(int(round(time.time() * 1000)))
    token = 'wwesldfs29aniversaryvdld29'
    sysCode = 'MCS-MIMP-CORE'
    data = f'token={token}&timestamp={timestamp}&sysCode={sysCode}'
    signature = hashlib.md5(data.encode()).hexdigest()
    
    headers.update({
        'sysCode': sysCode,
        'timestamp': timestamp,
        'signature': signature
    })
    
    url = 'https://mcs-mimp-web.sf-express.com/mcs-mimp/commonPost/~memberNonactivity~receiveExchangeIndexService~taskDetail'
    json_data = {}
    
    try:
        print(f"发送采蜜任务请求...")
        response = session.post(url, headers=headers, json=json_data, timeout=30)
        
        print(f"采蜜任务响应状态: {response.status_code}")
        print(f"采蜜任务响应内容: {response.text[:200]}...")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"采蜜任务JSON响应: {result}")
                
                if result.get('success') == True:
                    print(f"✅ 采蜜任务获取成功")
                    if 'obj' in result and 'list' in result['obj']:
                        tasks = result['obj']['list']
                        print(f"任务数量: {len(tasks)}")
                        for task in tasks:
                            task_type = task.get('taskType', '未知')
                            status = task.get('status', '未知')
                            print(f"  任务: {task_type} - 状态: {status}")
                    return True
                else:
                    error_msg = result.get('errorMessage', '无错误信息')
                    print(f"❌ 采蜜任务获取失败: {error_msg}")
                    return False
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                return False
        else:
            print(f"❌ 采蜜任务请求失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 采蜜任务请求异常: {e}")
        return False

if __name__ == "__main__":
    try:
        # 测试登录
        login_success, session = test_url()
        
        if login_success:
            # 测试采蜜任务
            honey_success = test_honey_task(session)
            
            print(f"\n" + "=" * 60)
            print("测试结果")
            print("=" * 60)
            print(f"登录: {'✅ 成功' if login_success else '❌ 失败'}")
            print(f"采蜜任务: {'✅ 成功' if honey_success else '❌ 失败'}")
            
            if login_success and honey_success:
                print(f"\n🎉 所有测试都成功！URL是有效的")
            elif login_success and not honey_success:
                print(f"\n⚠️ 登录成功但采蜜任务失败，可能是权限或签名问题")
            else:
                print(f"\n❌ 测试失败")
        else:
            print(f"\n❌ 登录失败，无法继续测试")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
