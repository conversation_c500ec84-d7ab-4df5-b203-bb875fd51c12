#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试黑号检测逻辑
"""

def test_black_number_detection():
    """测试黑号检测逻辑"""
    print("=== 测试黑号检测逻辑 ===")
    
    # 测试用例
    test_cases = [
        {
            "name": "原有黑号条件",
            "response": {
                "errorCode": "100012",
                "errorMessage": "用户手机号校验未通过",
                "success": False
            },
            "expected": "黑号"
        },
        {
            "name": "新增黑号条件1",
            "response": {
                "errorCode": "100012",
                "errorMessage": "暂无参与资格",
                "notSuccess": True,
                "success": False
            },
            "expected": "黑号"
        },
        {
            "name": "新增黑号条件2",
            "response": {
                "date": "2025-07-04 12:34:53",
                "errorCode": "100012",
                "errorMessage": "暂无参与资格",
                "notSuccess": True,
                "success": False
            },
            "expected": "黑号"
        },
        {
            "name": "白号条件",
            "response": {
                "success": True,
                "obj": {
                    "usableHoney": 1000
                }
            },
            "expected": "白号"
        },
        {
            "name": "未知状态",
            "response": {
                "errorCode": "500",
                "errorMessage": "系统错误",
                "success": False
            },
            "expected": "未知"
        }
    ]
    
    # 测试逻辑
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}. 测试 {case['name']}:")
        result = case['response']
        
        # 模拟检测逻辑
        error_code = result.get('errorCode')
        error_message = result.get('errorMessage', '')
        not_success = result.get('notSuccess', False)
        success = result.get('success', False)
        
        # 黑号判断条件
        is_black_number = (
            # 原有条件：用户手机号校验未通过
            (error_code == '100012' and '用户手机号校验未通过' in error_message) or
            # 新增条件：暂无参与资格
            (error_code == '100012' and '暂无参与资格' in error_message) or
            # 新增条件：notSuccess为true且success为false
            (not_success == True and success == False and error_code == '100012')
        )
        
        if is_black_number:
            detected_status = "黑号"
        elif success == True:
            detected_status = "白号"
        else:
            detected_status = "未知"
        
        # 输出结果
        print(f"   响应: {result}")
        print(f"   预期: {case['expected']}")
        print(f"   检测: {detected_status}")
        
        if detected_status == case['expected']:
            print(f"   结果: ✅ 通过")
        else:
            print(f"   结果: ❌ 失败")

def test_white_list_optimization():
    """测试白号优化逻辑"""
    print("\n=== 测试白号优化逻辑 ===")
    
    # 创建测试白号文件
    test_white_content = """13800138000----member1----https://example.com/1
13900139000----member2----https://example.com/2
13700137000----member3----https://example.com/3"""
    
    with open("test_白号.txt", 'w', encoding='utf-8') as f:
        f.write(test_white_content)
    
    print("1. 创建测试白号文件")
    print("   内容:")
    for line in test_white_content.split('\n'):
        parts = line.split('----')
        if len(parts) >= 1:
            phone = parts[0]
            print(f"   - {phone[:3]}****{phone[-4:]}")
    
    # 模拟加载白号列表
    white_phones = set()
    for line in test_white_content.split('\n'):
        if line.strip():
            parts = line.split('----')
            if len(parts) >= 1:
                phone = parts[0]
                white_phones.add(phone)
    
    print(f"\n2. 加载白号列表: {len(white_phones)} 个")
    
    # 测试检查逻辑
    test_phones = [
        "13800138000",  # 在白号列表中
        "13900139000",  # 在白号列表中
        "15800158000",  # 不在白号列表中
    ]
    
    print("\n3. 测试检查逻辑:")
    for phone in test_phones:
        if phone in white_phones:
            print(f"   {phone[:3]}****{phone[-4:]}: ⚡ 跳过检测 (已知白号)")
        else:
            print(f"   {phone[:3]}****{phone[-4:]}: 🔍 需要检测")
    
    # 清理测试文件
    import os
    if os.path.exists("test_白号.txt"):
        os.remove("test_白号.txt")
        print("\n4. 清理测试文件完成")

if __name__ == "__main__":
    print("🧪 开始测试黑号检测优化...")
    print()
    
    test_black_number_detection()
    test_white_list_optimization()
    
    print("\n✅ 测试完成！")
