import hashlib
import json
import os
import random
import time
from datetime import datetime
import requests
from requests.packages.urllib3.exceptions import InsecureRequestWarning

# 禁用安全请求警告
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

class BlackNumberChecker:
    def __init__(self, line_info, index):
        self.index = index + 1
        self.line_info = line_info  # 保存原始行信息

        # 解析格式：手机号----memberId----URL
        parts = line_info.split('----')
        if len(parts) >= 3:
            self.phone_raw = parts[0]
            self.member_id = parts[1]
            self.url = parts[2]
        else:
            raise ValueError(f"格式错误: {line_info}")
        
        self.s = requests.session()
        self.s.verify = False
        
        self.headers = {
            'Host': 'mcs-mimp-web.sf-express.com',
            'upgrade-insecure-requests': '1',
            'user-agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/50.0.2661.87 Safari/537.36',
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'accept-language': 'zh-CN,zh;q=0.9',
            'accept-encoding': 'gzip, deflate, br',
            'connection': 'keep-alive',
            'cache-control': 'max-age=0',
        }
        
        self.user_id = ''
        self.phone = ''
        self.mobile = ''
        self.is_black = False
        
    def login(self):
        """登录获取用户信息"""
        try:
            print(f'🔗 账号{self.index}开始登录检测')
            
            # 登录获取cookies
            ress = self.s.get(self.url, headers=self.headers, timeout=30)
            print(f'📊 账号{self.index}响应状态: {ress.status_code}')
            
            cookies = self.s.cookies.get_dict()
            print(f'🍪 账号{self.index}收到cookies数量: {len(cookies)}')
            
            # 获取关键信息
            self.user_id = cookies.get('_login_user_id_', '')
            self.phone = cookies.get('_login_mobile_', '')
            
            if self.phone:
                self.mobile = self.phone[:3] + "*" * 4 + self.phone[7:]
                print(f'👤 账号{self.index}:【{self.mobile}】登陆成功')
                return True
            else:
                # 如果没有获取到手机号，使用原始手机号
                if hasattr(self, 'phone_raw') and self.phone_raw:
                    self.phone = self.phone_raw
                    self.mobile = self.phone[:3] + "*" * 4 + self.phone[7:]
                    print(f'👤 账号{self.index}:【{self.mobile}】使用原始手机号')
                    return True
                print(f'❌ 账号{self.index}获取用户信息失败')
                return False
                
        except Exception as e:
            print(f'❌ 账号{self.index}登录异常: {str(e)}')
            return False
    
    def getSign(self):
        """生成签名"""
        timestamp = str(int(round(time.time() * 1000)))
        token = 'wwesldfs29aniversaryvdld29'
        sysCode = 'MCS-MIMP-CORE'
        data = f'token={token}&timestamp={timestamp}&sysCode={sysCode}'
        signature = hashlib.md5(data.encode()).hexdigest()
        
        sign_data = {
            'syscode': sysCode,
            'timestamp': timestamp,
            'signature': signature,
            'platform': 'WEIXIN',
            'channel': 'weixin'
        }
        self.headers.update(sign_data)
        return sign_data
    
    def check_black_number(self):
        """检测是否为黑号"""
        try:
            # 更新请求头
            self.getSign()
            self.headers.update({
                'Content-Type': 'application/json;charset=UTF-8',
                'Accept': 'application/json, text/plain, */*',
                'Origin': 'https://mcs-mimp-web.sf-express.com',
                'Referer': 'https://mcs-mimp-web.sf-express.com/inboxPresentInterMit',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Dest': 'empty',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) UnifiedPCWindowsWechat(0xf2540517) XWEB/13909 Flue'
            })
            
            # 请求检测接口
            url = 'https://mcs-mimp-web.sf-express.com/mcs-mimp/commonPost/~memberNonactivity~receiveExchangeIndexService~indexData'
            data = {}
            
            print(f'🔍 账号{self.index}开始检测黑号状态...')
            response = self.s.post(url, headers=self.headers, json=data, timeout=30)
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    
                    # 检查是否为黑号 - 扩展黑号识别条件
                    error_code = result.get('errorCode')
                    error_message = result.get('errorMessage', '')
                    not_success = result.get('notSuccess', False)
                    success = result.get('success', False)

                    # 黑号判断条件
                    is_black_number = (
                        # 原有条件：用户手机号校验未通过
                        (error_code == '100012' and '用户手机号校验未通过' in error_message) or
                        # 新增条件：暂无参与资格
                        (error_code == '100012' and '暂无参与资格' in error_message) or
                        # 新增条件：notSuccess为true且success为false
                        (not_success == True and success == False and error_code == '100012')
                    )

                    if is_black_number:
                        self.is_black = True
                        print(f'❌ 账号{self.index}【{self.mobile}】检测结果: 黑号 - {error_message}')
                        return True, "黑号"
                    elif success == True:
                        self.is_black = False
                        usable_honey = result.get('obj', {}).get('usableHoney', 0)
                        print(f'✅ 账号{self.index}【{self.mobile}】检测结果: 白号 - 可用丰蜜: {usable_honey}')
                        return True, "白号"
                    else:
                        print(f'⚠️ 账号{self.index}【{self.mobile}】检测结果: 未知状态 - {error_message}')
                        print(f'🔍 详细信息: errorCode={error_code}, success={success}, notSuccess={not_success}')
                        return True, "未知"
                        
                except json.JSONDecodeError:
                    print(f'❌ 账号{self.index}【{self.mobile}】响应解析失败: {response.text[:200]}')
                    return False, "解析失败"
            else:
                print(f'❌ 账号{self.index}【{self.mobile}】请求失败，状态码: {response.status_code}')
                return False, "请求失败"
                
        except Exception as e:
            print(f'❌ 账号{self.index}【{self.mobile}】检测异常: {str(e)}')
            return False, "检测异常"
    
    def check(self):
        """执行完整检测流程"""
        # 先登录
        if not self.login():
            return False, "登录失败", self.info
        
        # 等待一下避免请求过快
        time.sleep(random.uniform(1, 3))
        
        # 检测黑号
        success, status = self.check_black_number()
        if success:
            return True, status, self.line_info
        else:
            return False, status, self.line_info

def load_white_list():
    """加载已知的白号列表"""
    white_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "白号.txt")
    white_phones = set()

    if os.path.exists(white_file):
        try:
            with open(white_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        # 解析格式：手机号----memberId----URL，提取手机号
                        parts = line.split('----')
                        if len(parts) >= 1:
                            phone = parts[0]
                            white_phones.add(phone)
            print(f"📋 已加载 {len(white_phones)} 个已知白号")
        except Exception as e:
            print(f"❌ 读取白号文件失败: {str(e)}")
    else:
        print("📋 未找到白号.txt文件，将进行完整检测")

    return white_phones

def read_lines_from_file():
    """从登录成功_链接二.txt文件读取完整行信息"""
    file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "登录成功_白_链接二.txt")

    if not os.path.exists(file_path):
        print(f"❌ 未找到文件: {file_path}")
        return []

    lines = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue

                # 解析格式：手机号----memberId----URL
                parts = line.split('----')
                if len(parts) >= 3:
                    phone = parts[0]
                    member_id = parts[1]
                    url = parts[2]
                    lines.append(line)
                    print(f"📱 读取账号 {line_num}: {phone[:3]}****{phone[-4:]} -> 信息已获取")
                else:
                    print(f"⚠️ 第{line_num}行格式错误，跳过: {line}")

        print(f"✅ 成功从文件读取到 {len(lines)} 个账号")
        return lines

    except Exception as e:
        print(f"❌ 读取文件失败: {str(e)}")
        return []

def save_single_result(account_line, result_type):
    """实时保存单个检测结果"""
    files = {
        "白号": "白号.txt",
        "黑号": "黑号.txt",
        "未知": "未知状态.txt",
        "失败": "检测失败.txt"
    }

    if result_type in files:
        try:
            with open(files[result_type], 'a', encoding='utf-8') as f:
                f.write(f"{account_line}\n")

            # 解析手机号用于显示
            parts = account_line.split('----')
            if len(parts) >= 1:
                phone = parts[0]
                mobile_display = phone[:3] + "*" * 4 + phone[7:]
                print(f"📝 已将账号 {mobile_display} 保存到 {files[result_type]}")
        except Exception as e:
            print(f"保存结果失败: {str(e)}")

def load_processed_accounts():
    """加载已处理的账号列表"""
    processed_file = "已处理账号.txt"
    processed_accounts = set()

    if os.path.exists(processed_file):
        try:
            with open(processed_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        # 提取手机号作为唯一标识
                        parts = line.split('----')
                        if len(parts) >= 1:
                            phone = parts[0]
                            processed_accounts.add(phone)
        except Exception as e:
            print(f"读取已处理账号失败: {str(e)}")

    return processed_accounts

def save_processed_account(account_line):
    """保存已处理的账号"""
    processed_file = "已处理账号.txt"
    try:
        with open(processed_file, 'a', encoding='utf-8') as f:
            f.write(f"{account_line}\n")
    except Exception as e:
        print(f"保存已处理账号失败: {str(e)}")

def get_file_modification_time(file_path):
    """获取文件修改时间"""
    try:
        if os.path.exists(file_path):
            return os.path.getmtime(file_path)
        return 0
    except:
        return 0

def main():
    print("=" * 50)
    print("🔍 顺丰黑号检测工具 (实时版)")
    print("📝 功能: 实时检测账号并保存结果")
    print("🚀 特性: 增量处理、实时保存、持续运行")
    print("=" * 50)

    # 统计变量
    total_processed = 0
    total_white = 0
    total_black = 0
    total_unknown = 0
    total_failed = 0

    last_file_time = 0

    print("🔄 开始实时监控模式...")
    print("💡 程序将持续运行，监控文件变化")
    print("💡 只处理新增账号，不重复检测")
    print("=" * 50)

    while True:
        try:
            # 检查文件是否有更新
            file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "登录成功_白_链接二.txt")
            current_file_time = get_file_modification_time(file_path)

            if current_file_time != last_file_time:
                print(f"\n📁 检测到文件更新 ({datetime.now().strftime('%H:%M:%S')})")

                # 加载已知白号和已处理账号
                known_white_phones = load_white_list()
                processed_phones = load_processed_accounts()

                # 读取所有账号
                lines = read_lines_from_file()
                if not lines:
                    print("❌ 未找到有效的账号数据")
                    time.sleep(30)
                    continue

                # 筛选出新增的账号
                new_lines = []
                for line in lines:
                    parts = line.split('----')
                    if len(parts) >= 1:
                        phone = parts[0]
                        if phone not in processed_phones:
                            new_lines.append(line)

                if not new_lines:
                    print("📋 没有新增账号需要处理")
                    last_file_time = current_file_time
                    time.sleep(30)
                    continue

                print(f"📊 发现 {len(new_lines)} 个新增账号，开始处理...")

                # 处理新增账号
                round_white = 0
                round_black = 0
                round_unknown = 0
                round_failed = 0
                round_skipped = 0

                for index, line in enumerate(new_lines):
                    try:
                        # 解析手机号
                        parts = line.split('----')
                        if len(parts) >= 1:
                            phone = parts[0]
                            mobile_display = phone[:3] + "*" * 4 + phone[7:]

                            # 检查是否为已知白号
                            if phone in known_white_phones:
                                save_single_result(line, "白号")
                                save_processed_account(line)
                                round_white += 1
                                round_skipped += 1
                                print(f'⚡ 账号{index + 1}【{mobile_display}】已知白号，跳过检测')
                                continue

                        # 实际检测
                        print(f'🔍 检测账号{index + 1}【{mobile_display}】...')
                        checker = BlackNumberChecker(line, index)
                        success, status, original_line = checker.check()

                        # 实时保存结果
                        if success:
                            if status == "白号":
                                save_single_result(original_line, "白号")
                                round_white += 1
                            elif status == "黑号":
                                save_single_result(original_line, "黑号")
                                round_black += 1
                            else:  # 未知状态
                                save_single_result(original_line, "未知")
                                round_unknown += 1
                        else:
                            save_single_result(original_line, "失败")
                            round_failed += 1

                        # 保存到已处理列表
                        save_processed_account(original_line)

                        # 等待避免请求过快
                        if index < len(new_lines) - 1:
                            time.sleep(random.uniform(2, 5))

                    except Exception as e:
                        print(f"❌ 账号{index + 1}检测异常: {str(e)}")
                        save_single_result(line, "失败")
                        save_processed_account(line)
                        round_failed += 1

                # 更新统计
                total_processed += len(new_lines)
                total_white += round_white
                total_black += round_black
                total_unknown += round_unknown
                total_failed += round_failed

                # 输出本轮统计
                print("=" * 30)
                print(f"📊 本轮处理完成:")
                print(f"✅ 白号: {round_white} 个 (其中 {round_skipped} 个跳过检测)")
                print(f"❌ 黑号: {round_black} 个")
                print(f"⚠️ 未知状态: {round_unknown} 个")
                print(f"💥 检测失败: {round_failed} 个")
                print(f"📱 本轮总计: {len(new_lines)} 个")
                print("=" * 30)
                print(f"� 累计统计:")
                print(f"✅ 累计白号: {total_white} 个")
                print(f"❌ 累计黑号: {total_black} 个")
                print(f"⚠️ 累计未知: {total_unknown} 个")
                print(f"💥 累计失败: {total_failed} 个")
                print(f"📱 累计处理: {total_processed} 个")
                print("=" * 50)

                last_file_time = current_file_time

            # 等待下次检查
            print(f"⏳ 等待文件更新... ({datetime.now().strftime('%H:%M:%S')})")
            time.sleep(30)

        except KeyboardInterrupt:
            print("\n👋 用户中断，程序退出")
            break
        except Exception as e:
            print(f"❌ 程序异常: {str(e)}")
            time.sleep(30)

    print("🎉 黑号检测程序结束！")

if __name__ == '__main__':
    main()
