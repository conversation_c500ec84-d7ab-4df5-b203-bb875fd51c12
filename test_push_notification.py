#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试推送通知功能
"""

import os
import requests
from datetime import datetime

def test_pushplus_api():
    """测试PushPlus API"""
    print("=== 测试PushPlus API ===")
    
    token = "0b0bf6e912db45e5a6cae21b88145392"
    
    # 模拟兑换成功的账号数据
    success_accounts = [
        {'mobile': '138****8000', 'time': '14:30:15'},
        {'mobile': '139****9000', 'time': '14:31:22'},
        {'mobile': '158****8000', 'time': '14:32:45'}
    ]
    
    success_count = len(success_accounts)
    today = datetime.now().strftime('%Y-%m-%d')
    current_time = datetime.now().strftime('%H:%M:%S')
    
    # 构建推送标题
    title = f"🎯 顺丰23元券兑换成功通知 ({success_count}个)"
    
    # 提取手机号列表
    mobile_list = [account['mobile'] for account in success_accounts]
    mobile_text = "、".join(mobile_list)
    
    # 构建推送内容
    content = f"""
    <h2>🎉 兑换成功汇总</h2>
    <p><strong>📅 日期:</strong> {today}</p>
    <p><strong>⏰ 时间:</strong> {current_time}</p>
    <p><strong>📊 成功数量:</strong> {success_count} 个账号</p>
    
    <h3>📱 成功账号:</h3>
    <p style="font-size: 16px; line-height: 1.5;">{mobile_text}</p>
    
    <p style="margin-top: 20px;">
        <strong>💡 提示:</strong> 详细账号信息已保存到 <code>兑换23成功_{today}.txt</code> 文件中
    </p>
    """
    
    print(f"推送标题: {title}")
    print(f"推送内容预览:")
    print(f"  日期: {today}")
    print(f"  时间: {current_time}")
    print(f"  成功数量: {success_count} 个账号")
    print(f"  成功账号: {mobile_text}")
    
    # 发送测试推送
    try:
        url = "https://www.pushplus.plus/send"
        data = {
            "token": token,
            "title": title,
            "content": content,
            "template": "html"
        }
        
        headers = {
            "Content-Type": "application/json"
        }
        
        print(f"\n发送推送到 PushPlus...")
        response = requests.post(url, json=data, headers=headers, timeout=10)
        response.raise_for_status()
        
        result = response.json()
        if result.get('code') == 200:
            print(f"✅ 推送发送成功！")
            print(f"   响应: {result}")
        else:
            print(f"❌ 推送发送失败: {result.get('msg', '未知错误')}")
            print(f"   响应: {result}")
            
    except Exception as e:
        print(f"❌ 推送发送异常: {str(e)}")

def demo_file_format():
    """演示新的文件格式"""
    print("\n=== 新文件格式演示 ===")
    
    today = datetime.now().strftime('%Y-%m-%d')
    filename = f"兑换23成功_{today}.txt"
    
    print(f"文件名: {filename}")
    print("文件格式: 手机号----memberid----url----时间")
    print("示例内容:")
    print("```")
    print("13800138000----member123----https://mcs-mimp-web.sf-express.com/demo1----14:30:15")
    print("13900139000----member456----https://mcs-mimp-web.sf-express.com/demo2----14:31:22")
    print("15800158000----member789----https://mcs-mimp-web.sf-express.com/demo3----14:32:45")
    print("13800138000----member123----https://mcs-mimp-web.sf-express.com/demo1----15:45:30")  # 同一账号可以多次兑换
    print("```")
    
    print("\n特点:")
    print("   ✅ 文件名带日期时间戳")
    print("   ✅ 每行记录带兑换时间")
    print("   ✅ 允许同一账号多次兑换")
    print("   ✅ 不检查重复，直接追加")

def demo_push_timing():
    """演示推送时机"""
    print("\n=== 推送时机演示 ===")
    
    scenarios = [
        {
            "mode": "传统模式",
            "description": "一次性处理所有账号",
            "timing": "所有账号处理完成后统一推送"
        },
        {
            "mode": "实时读取模式", 
            "description": "循环处理账号",
            "timing": "每轮处理完成后统一推送"
        }
    ]
    
    for scenario in scenarios:
        print(f"\n{scenario['mode']}:")
        print(f"   处理方式: {scenario['description']}")
        print(f"   推送时机: {scenario['timing']}")
    
    print(f"\n推送内容:")
    print(f"   📊 统计信息: 成功数量、日期时间")
    print(f"   📱 账号列表: 脱敏手机号，用顿号分隔")
    print(f"   💡 文件提示: 详细信息保存位置")

def demo_configuration():
    """演示配置方法"""
    print("\n=== 配置方法 ===")
    
    print("环境变量配置:")
    print("   PUSHPLUS_TOKEN=0b0bf6e912db45e5a6cae21b88145392")
    print("")
    print("Windows 设置方法:")
    print("   set PUSHPLUS_TOKEN=0b0bf6e912db45e5a6cae21b88145392")
    print("")
    print("Linux/Mac 设置方法:")
    print("   export PUSHPLUS_TOKEN=0b0bf6e912db45e5a6cae21b88145392")
    print("")
    print("代码中默认值:")
    print("   如果未设置环境变量，使用默认token: 0b0bf6e912db45e5a6cae21b88145392")

if __name__ == "__main__":
    print("🔔 推送通知功能测试")
    print("=" * 50)
    
    test_pushplus_api()
    demo_file_format()
    demo_push_timing()
    demo_configuration()
    
    print("\n" + "=" * 50)
    print("✅ 测试完成！")
    print("💡 如果推送成功，您应该会收到测试通知")
