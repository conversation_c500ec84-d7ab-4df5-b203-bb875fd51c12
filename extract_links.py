#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取链接脚本
从登录成功_链接二.txt文件中提取链接，每行一个
"""

def extract_links(input_file, output_file):
    """
    从输入文件中提取链接并保存到输出文件
    
    Args:
        input_file (str): 输入文件路径
        output_file (str): 输出文件路径
    """
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        links = []
        for line in lines:
            line = line.strip()
            if line:  # 跳过空行
                # 按照----分割，取第三部分（链接）
                parts = line.split('----')
                if len(parts) >= 3:
                    link = parts[2]
                    links.append(link)
        
        # 保存链接到输出文件
        with open(output_file, 'w', encoding='utf-8') as f:
            for link in links:
                f.write(link + '\n')
        
        print(f"成功提取 {len(links)} 个链接")
        print(f"链接已保存到: {output_file}")
        
        # 显示前3个链接作为预览
        if links:
            print("\n前3个链接预览:")
            for i, link in enumerate(links[:3], 1):
                print(f"{i}. {link}")
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {input_file}")
    except Exception as e:
        print(f"错误: {e}")

def main():
    """主函数"""
    input_file = "登录成功_链接二.txt"
    output_file = "extracted_links.txt"
    
    print("开始提取链接...")
    extract_links(input_file, output_file)

if __name__ == "__main__":
    main()
